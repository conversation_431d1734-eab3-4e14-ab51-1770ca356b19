#!/usr/bin/env node

// Migration script to move data from localStorage JSON files to SQLite
// Usage: node scripts/migrate-to-sqlite.js [backup-file.json]

import fs from 'fs';
import path from 'path';
import fetch from 'node-fetch';

// Sample data structure that would typically be in localStorage
const sampleData = {
  alumni: [
    {
      id: 'ALM20240001',
      userId: 'USR001',
      nim: '123456789',
      namaLengkap: '<PERSON>',
      programStudi: 'Teknik Informatika',
      fakultas: 'Teknik',
      tahunMasuk: 2020,
      tahunLulus: 2024,
      ipk: 3.75,
      email: '<EMAIL>',
      noTelepon: '081234567890',
      alamat: 'Jakarta',
      statusVerifikasi: 'verified',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z'
    },
    {
      id: 'ALM20240002',
      userId: 'USR002',
      nim: '987654321',
      namaLengkap: '<PERSON>',
      programStudi: 'Sistem Informasi',
      fakultas: 'Teknik',
      tahunMasuk: 2019,
      tahunLulus: 2023,
      ipk: 3.85,
      email: '<EMAIL>',
      noTelepon: '081987654321',
      alamat: 'Bandung',
      statusVerifikasi: 'verified',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z'
    }
  ],
  employment: [
    {
      id: 'EMP001',
      alumniId: 'ALM20240001',
      namaPerusahaan: 'Tech Corp',
      posisiJabatan: 'Software Developer',
      jenisUsaha: 'IT Services',
      gajiPertama: 8000000,
      gajiSaatIni: 12000000,
      tanggalMulaiKerja: '2024-02-01',
      statusPekerjaan: 'bekerja',
      relevansiPekerjaan: 'sangat_relevan'
    },
    {
      id: 'EMP002',
      alumniId: 'ALM20240002',
      namaPerusahaan: 'Digital Solutions',
      posisiJabatan: 'System Analyst',
      jenisUsaha: 'IT Consulting',
      gajiPertama: 7500000,
      gajiSaatIni: 11000000,
      tanggalMulaiKerja: '2023-03-01',
      statusPekerjaan: 'bekerja',
      relevansiPekerjaan: 'relevan'
    }
  ],
  users: [
    {
      id: 'USR001',
      username: 'john.doe',
      email: '<EMAIL>',
      role: 'alumni',
      namaLengkap: 'John Doe',
      createdAt: '2024-01-01T00:00:00.000Z',
      lastLogin: '2024-01-15T10:00:00.000Z'
    },
    {
      id: 'USR002',
      username: 'jane.smith',
      email: '<EMAIL>',
      role: 'alumni',
      namaLengkap: 'Jane Smith',
      createdAt: '2024-01-01T00:00:00.000Z',
      lastLogin: '2024-01-14T15:30:00.000Z'
    }
  ],
  surveys: [
    {
      id: 'SRV001',
      judul: 'Survey Kepuasan Alumni 2024',
      deskripsi: 'Survey untuk mengetahui kepuasan alumni terhadap program studi',
      tanggalMulai: '2024-01-01',
      tanggalSelesai: '2024-03-31',
      status: 'active',
      targetAlumni: ['ALM20240001', 'ALM20240002'],
      questions: [
        {
          id: 'Q1',
          type: 'rating',
          question: 'Bagaimana penilaian Anda terhadap kualitas pendidikan?',
          required: true
        },
        {
          id: 'Q2',
          type: 'text',
          question: 'Saran untuk perbaikan program studi',
          required: false
        }
      ]
    }
  ],
  settings: [
    {
      id: 'SET001',
      key: 'university_name',
      value: 'Universitas Pendidikan Indonesia Manado',
      description: 'Nama universitas',
      updatedAt: '2024-01-01T00:00:00.000Z'
    },
    {
      id: 'SET002',
      key: 'contact_email',
      value: '<EMAIL>',
      description: 'Email kontak',
      updatedAt: '2024-01-01T00:00:00.000Z'
    }
  ],
  survey_responses: [
    {
      id: 'RESP001',
      surveyId: 'SRV001',
      alumniId: 'ALM20240001',
      responses: {
        Q1: 5,
        Q2: 'Program studi sudah baik, perlu ditingkatkan praktikum'
      },
      submittedAt: '2024-01-15T10:00:00.000Z'
    }
  ]
};

async function migrateData(dataFile) {
  try {
    let data = sampleData;
    
    // If backup file is provided, read from it
    if (dataFile && fs.existsSync(dataFile)) {
      console.log(`📁 Reading data from ${dataFile}...`);
      const fileContent = fs.readFileSync(dataFile, 'utf8');
      data = JSON.parse(fileContent);
    } else {
      console.log('📊 Using sample data for migration...');
    }

    console.log('🔄 Starting migration to SQLite...');
    console.log(`📈 Data summary:`);
    console.log(`   - Alumni: ${data.alumni?.length || 0} records`);
    console.log(`   - Employment: ${data.employment?.length || 0} records`);
    console.log(`   - Users: ${data.users?.length || 0} records`);
    console.log(`   - Surveys: ${data.surveys?.length || 0} records`);
    console.log(`   - Settings: ${data.settings?.length || 0} records`);
    console.log(`   - Survey Responses: ${data.survey_responses?.length || 0} records`);

    // Make API call to migration endpoint
    const response = await fetch('http://localhost:3002/api/migration/from-json', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    });

    const result = await response.json();

    if (result.success) {
      console.log('✅ Migration completed successfully!');
      console.log('📊 Migration results:');
      console.log(`   - Alumni: ${result.data.alumni} records migrated`);
      console.log(`   - Employment: ${result.data.employment} records migrated`);
      console.log(`   - Users: ${result.data.users} records migrated`);
      console.log(`   - Surveys: ${result.data.surveys} records migrated`);
      console.log(`   - Settings: ${result.data.settings} records migrated`);
      console.log(`   - Survey Responses: ${result.data.survey_responses} records migrated`);
      
      if (result.data.errors && result.data.errors.length > 0) {
        console.log('⚠️  Errors encountered:');
        result.data.errors.forEach(error => console.log(`   - ${error}`));
      }
    } else {
      console.error('❌ Migration failed:', result.message);
      if (result.error) {
        console.error('Error details:', result.error);
      }
    }

  } catch (error) {
    console.error('❌ Migration failed with error:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('💡 Make sure the SQLite server is running:');
      console.error('   npm run server:dev');
    }
  }
}

// Check if server is running
async function checkServer() {
  try {
    const response = await fetch('http://localhost:3002/api/health');
    const result = await response.json();
    return result.success;
  } catch (error) {
    return false;
  }
}

// Main execution
async function main() {
  console.log('🚀 SQLite Migration Tool');
  console.log('========================');

  // Check if server is running
  const serverRunning = await checkServer();
  if (!serverRunning) {
    console.error('❌ SQLite server is not running!');
    console.error('💡 Please start the server first:');
    console.error('   npm run server:dev');
    process.exit(1);
  }

  console.log('✅ SQLite server is running');

  // Get backup file from command line argument
  const backupFile = process.argv[2];
  
  if (backupFile && !fs.existsSync(backupFile)) {
    console.error(`❌ Backup file not found: ${backupFile}`);
    process.exit(1);
  }

  await migrateData(backupFile);
}

main().catch(console.error);
