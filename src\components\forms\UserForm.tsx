import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { UserData } from '@/utils/dataManager';

interface UserFormProps {
  user?: UserData;
  onSubmit: (data: Omit<UserData, 'id' | 'createdAt' | 'updatedAt'>) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

const UserForm: React.FC<UserFormProps> = ({ 
  user, 
  onSubmit, 
  onCancel, 
  isLoading = false 
}) => {
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    fullName: '',
    role: 'user' as 'admin' | 'user' | 'moderator',
    isActive: true,
    lastLogin: null as string | null,
    permissions: [] as string[]
  });

  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  const availablePermissions = [
    'read_alumni',
    'write_alumni',
    'delete_alumni',
    'read_surveys',
    'write_surveys',
    'delete_surveys',
    'read_employment',
    'write_employment',
    'delete_employment',
    'read_reports',
    'export_data',
    'manage_users',
    'system_settings'
  ];

  const permissionLabels: { [key: string]: string } = {
    'read_alumni': 'Lihat Data Alumni',
    'write_alumni': 'Edit Data Alumni',
    'delete_alumni': 'Hapus Data Alumni',
    'read_surveys': 'Lihat Survey',
    'write_surveys': 'Edit Survey',
    'delete_surveys': 'Hapus Survey',
    'read_employment': 'Lihat Data Pekerjaan',
    'write_employment': 'Edit Data Pekerjaan',
    'delete_employment': 'Hapus Data Pekerjaan',
    'read_reports': 'Lihat Laporan',
    'export_data': 'Export Data',
    'manage_users': 'Kelola Pengguna',
    'system_settings': 'Pengaturan Sistem'
  };

  useEffect(() => {
    if (user) {
      setFormData({
        username: user.username || '',
        email: user.email || '',
        fullName: user.namaLengkap || '',
        role: user.role || 'alumni',
        isActive: true,
        lastLogin: user.lastLogin || null,
        permissions: []
      });
    }
  }, [user]);

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.username.trim()) newErrors.username = 'Username wajib diisi';
    if (formData.username.length < 3) newErrors.username = 'Username minimal 3 karakter';
    if (!formData.email.trim()) newErrors.email = 'Email wajib diisi';
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Format email tidak valid';
    }
    if (!formData.fullName.trim()) newErrors.fullName = 'Nama lengkap wajib diisi';

    // Password validation for new users
    if (!user) {
      if (!password) newErrors.password = 'Password wajib diisi';
      if (password.length < 6) newErrors.password = 'Password minimal 6 karakter';
      if (password !== confirmPassword) newErrors.confirmPassword = 'Konfirmasi password tidak cocok';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      const submitData = {
        ...formData,
        ...(password && { password }) // Only include password if provided
      };
      onSubmit(submitData);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handlePermissionChange = (permission: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      permissions: checked 
        ? [...prev.permissions, permission]
        : prev.permissions.filter(p => p !== permission)
    }));
  };

  const handleRoleChange = (role: string) => {
    handleInputChange('role', role);
    
    // Auto-assign permissions based on role
    if (role === 'admin') {
      setFormData(prev => ({ ...prev, permissions: [...availablePermissions] }));
    } else if (role === 'moderator') {
      setFormData(prev => ({ 
        ...prev, 
        permissions: [
          'read_alumni', 'write_alumni',
          'read_surveys', 'write_surveys',
          'read_employment', 'write_employment',
          'read_reports'
        ]
      }));
    } else {
      setFormData(prev => ({ 
        ...prev, 
        permissions: ['read_alumni', 'read_surveys', 'read_employment', 'read_reports']
      }));
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>{user ? 'Edit Pengguna' : 'Tambah Pengguna Baru'}</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="username">Username *</Label>
              <Input
                id="username"
                value={formData.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                className={errors.username ? 'border-red-500' : ''}
                disabled={!!user} // Disable username editing for existing users
              />
              {errors.username && <p className="text-red-500 text-sm mt-1">{errors.username}</p>}
            </div>

            <div>
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={errors.email ? 'border-red-500' : ''}
              />
              {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
            </div>

            <div className="md:col-span-2">
              <Label htmlFor="fullName">Nama Lengkap *</Label>
              <Input
                id="fullName"
                value={formData.fullName}
                onChange={(e) => handleInputChange('fullName', e.target.value)}
                className={errors.fullName ? 'border-red-500' : ''}
              />
              {errors.fullName && <p className="text-red-500 text-sm mt-1">{errors.fullName}</p>}
            </div>
          </div>

          {/* Password Fields (only for new users or when changing password) */}
          {!user && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="password">Password *</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => {
                    setPassword(e.target.value);
                    if (errors.password) {
                      setErrors(prev => ({ ...prev, password: '' }));
                    }
                  }}
                  className={errors.password ? 'border-red-500' : ''}
                />
                {errors.password && <p className="text-red-500 text-sm mt-1">{errors.password}</p>}
              </div>

              <div>
                <Label htmlFor="confirmPassword">Konfirmasi Password *</Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  value={confirmPassword}
                  onChange={(e) => {
                    setConfirmPassword(e.target.value);
                    if (errors.confirmPassword) {
                      setErrors(prev => ({ ...prev, confirmPassword: '' }));
                    }
                  }}
                  className={errors.confirmPassword ? 'border-red-500' : ''}
                />
                {errors.confirmPassword && <p className="text-red-500 text-sm mt-1">{errors.confirmPassword}</p>}
              </div>
            </div>
          )}

          {/* Role and Status */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="role">Role</Label>
              <Select value={formData.role} onValueChange={handleRoleChange}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="user">User</SelectItem>
                  <SelectItem value="moderator">Moderator</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2 mt-6">
              <Switch
                checked={formData.isActive}
                onCheckedChange={(checked) => handleInputChange('isActive', checked)}
              />
              <Label>Akun Aktif</Label>
            </div>
          </div>

          {/* Permissions */}
          <div>
            <Label className="text-base font-medium">Permissions</Label>
            <div className="mt-2 space-y-2 max-h-48 overflow-y-auto border rounded-md p-3">
              {availablePermissions.map(permission => (
                <div key={permission} className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id={permission}
                    checked={formData.permissions.includes(permission)}
                    onChange={(e) => handlePermissionChange(permission, e.target.checked)}
                    className="rounded border-gray-300"
                  />
                  <Label htmlFor={permission} className="text-sm">
                    {permissionLabels[permission] || permission}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Last Login Info for existing users */}
          {user && user.lastLogin && (
            <div>
              <Label>Terakhir Login</Label>
              <p className="text-sm text-gray-600 mt-1">
                {new Date(user.lastLogin).toLocaleString('id-ID')}
              </p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4">
            <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
              Batal
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Menyimpan...' : user ? 'Update Pengguna' : 'Tambah Pengguna'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default UserForm;
