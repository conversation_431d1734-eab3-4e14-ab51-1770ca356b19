import React, { useState, useEffect } from 'react';
import { Plus, Search, Filter, Building, MapPin, Calendar, Edit, Trash2, Eye } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import Layout from '@/components/Layout';
import EmploymentForm from '@/components/forms/EmploymentForm';
import { dataManager, EmploymentData, AlumniData } from '@/utils/dataManager';

const Employment = () => {
  const [employment, setEmployment] = useState<EmploymentData[]>([]);
  const [alumni, setAlumni] = useState<AlumniData[]>([]);
  const [filteredEmployment, setFilteredEmployment] = useState<EmploymentData[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [selectedEmployment, setSelectedEmployment] = useState<EmploymentData | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    filterEmployment();
  }, [employment, searchTerm, filterType]);

  const loadData = () => {
    const employmentData = dataManager.getEmploymentData();
    const alumniData = dataManager.getAlumniData();
    setEmployment(employmentData);
    setAlumni(alumniData);
  };

  const filterEmployment = () => {
    let filtered = employment;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(item => {
        const alumnus = alumni.find(a => a.id === item.alumniId);
        return (
          (item.namaPerusahaan || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
          (item.posisiJabatan || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
          (item.jenisUsaha || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
          (alumnus && (alumnus.namaLengkap || '').toLowerCase().includes(searchTerm.toLowerCase()))
        );
      });
    }

    // Type filter
    if (filterType !== 'all') {
      filtered = filtered.filter(item => item.statusPekerjaan === filterType);
    }

    setFilteredEmployment(filtered);
  };

  const handleAddEmployment = () => {
    setSelectedEmployment(null);
    setIsFormOpen(true);
  };

  const handleViewEmployment = (employment: EmploymentData) => {
    setSelectedEmployment(employment);
    setIsViewDialogOpen(true);
  };

  const handleEditEmployment = (employment: EmploymentData) => {
    setSelectedEmployment(employment);
    setIsFormOpen(true);
  };

  const handleDeleteEmployment = (employment: EmploymentData) => {
    setSelectedEmployment(employment);
    setIsDeleteDialogOpen(true);
  };

  const handleFormSubmit = async (formData: any) => {
    setIsLoading(true);
    try {
      // Map form data to EmploymentData interface
      const employmentData: Omit<EmploymentData, 'id'> = {
        alumniId: formData.alumniId || '',
        namaPerusahaan: formData.companyName || '',
        posisiJabatan: formData.position || '',
        jenisUsaha: formData.industry || '',
        gajiPertama: parseInt(formData.salary) || 0,
        gajiSaatIni: parseInt(formData.salary) || 0,
        tanggalMulaiKerja: formData.startDate || '',
        statusPekerjaan: formData.isCurrentJob ? 'bekerja' : 'tidak_bekerja',
        relevansiPekerjaan: 'relevan' as 'sangat_relevan' | 'relevan' | 'kurang_relevan' | 'tidak_relevan'
      };

      if (selectedEmployment) {
        // Update existing employment
        dataManager.updateEmployment(selectedEmployment.id, employmentData);
      } else {
        // Add new employment
        dataManager.addEmployment(employmentData);
      }
      loadData();
      setIsFormOpen(false);
      setSelectedEmployment(null);
    } catch (error) {
      console.error('Error saving employment:', error);
      alert('Gagal menyimpan data pekerjaan');
    } finally {
      setIsLoading(false);
    }
  };

  const confirmDelete = async () => {
    if (selectedEmployment) {
      setIsLoading(true);
      try {
        dataManager.deleteEmployment(selectedEmployment.id);
        loadData();
        setIsDeleteDialogOpen(false);
        setSelectedEmployment(null);
      } catch (error) {
        console.error('Error deleting employment:', error);
        alert('Gagal menghapus data pekerjaan');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const getEmploymentTypeBadge = (status: string) => {
    const statusMap = {
      bekerja: { label: 'Bekerja', variant: 'default' as const },
      tidak_bekerja: { label: 'Tidak Bekerja', variant: 'secondary' as const },
      wirausaha: { label: 'Wirausaha', variant: 'outline' as const }
    };

    const statusInfo = statusMap[status as keyof typeof statusMap] || { label: status, variant: 'default' as const };
    return <Badge variant={statusInfo.variant}>{statusInfo.label}</Badge>;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'short'
    });
  };

  const getAlumniName = (alumniId: string) => {
    const alumnus = alumni.find(a => a.id === alumniId);
    return alumnus ? alumnus.namaLengkap : 'Alumni tidak ditemukan';
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Data Pekerjaan Alumni</h1>
            <p className="text-gray-600">Kelola data pekerjaan dan karir alumni</p>
          </div>
          <Button className="university-gradient" onClick={handleAddEmployment}>
            <Plus className="h-4 w-4 mr-2" />
            Tambah Data Pekerjaan
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Building className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Pekerjaan</p>
                  <p className="text-2xl font-bold text-gray-900">{employment.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <MapPin className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Sedang Bekerja</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {employment.filter(e => e.isCurrentJob).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Calendar className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Full Time</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {employment.filter(e => e.employmentType === 'full_time').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Building className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Perusahaan Unik</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {new Set(employment.map(e => e.companyName)).size}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Cari berdasarkan nama alumni, perusahaan, posisi, atau industri..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Select value={filterType} onValueChange={setFilterType}>
                  <SelectTrigger className="w-[180px]">
                    <Filter className="h-4 w-4 mr-2" />
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Semua Tipe</SelectItem>
                    <SelectItem value="full_time">Full Time</SelectItem>
                    <SelectItem value="part_time">Part Time</SelectItem>
                    <SelectItem value="contract">Kontrak</SelectItem>
                    <SelectItem value="freelance">Freelance</SelectItem>
                    <SelectItem value="internship">Magang</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Employment List */}
        <Card>
          <CardHeader>
            <CardTitle>Data Pekerjaan ({filteredEmployment.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {filteredEmployment.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Building className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>Belum ada data pekerjaan yang tersedia</p>
                <Button className="mt-4 university-gradient" onClick={handleAddEmployment}>
                  <Plus className="h-4 w-4 mr-2" />
                  Tambah Data Pekerjaan Pertama
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredEmployment.map((item) => (
                  <div key={item.id} className="border rounded-lg p-4 hover:bg-gray-50">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h3 className="font-semibold text-lg">{item.posisiJabatan}</h3>
                          {getEmploymentTypeBadge(item.statusPekerjaan)}
                          {item.statusPekerjaan === 'bekerja' && (
                            <Badge variant="default" className="bg-green-600">Aktif</Badge>
                          )}
                        </div>

                        <div className="text-gray-600 space-y-1">
                          <p className="font-medium">{item.namaPerusahaan}</p>
                          <p className="text-sm">Alumni: {getAlumniName(item.alumniId)}</p>
                          <p className="text-sm">Jenis Usaha: {item.jenisUsaha}</p>
                          <p className="text-sm">Gaji Pertama: Rp {item.gajiPertama?.toLocaleString('id-ID') || 'Tidak diketahui'}</p>
                          <p className="text-sm">
                            Mulai Kerja: {formatDate(item.tanggalMulaiKerja)}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex space-x-2">
                        <Button variant="ghost" size="sm" title="Lihat Detail" onClick={() => handleViewEmployment(item)}>
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" title="Edit" onClick={() => handleEditEmployment(item)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="text-red-600 hover:text-red-700" 
                          title="Hapus"
                          onClick={() => handleDeleteEmployment(item)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Form Dialog */}
      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{selectedEmployment ? 'Edit Data Pekerjaan' : 'Tambah Data Pekerjaan Baru'}</DialogTitle>
          </DialogHeader>
          <EmploymentForm
            employment={selectedEmployment}
            alumni={alumni}
            onSubmit={handleFormSubmit}
            onCancel={() => setIsFormOpen(false)}
            isLoading={isLoading}
          />
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Konfirmasi Hapus</AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus data pekerjaan <strong>{selectedEmployment?.position}</strong> di <strong>{selectedEmployment?.companyName}</strong>? 
              Tindakan ini tidak dapat dibatalkan.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Batal</AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmDelete} 
              disabled={isLoading}
              className="bg-red-600 hover:bg-red-700"
            >
              {isLoading ? 'Menghapus...' : 'Hapus'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* View Employment Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Detail Pekerjaan Alumni</DialogTitle>
          </DialogHeader>
          {selectedEmployment && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Alumni ID</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedEmployment.alumniId}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Nama Perusahaan</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedEmployment.namaPerusahaan}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Posisi/Jabatan</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedEmployment.posisiJabatan}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Jenis Usaha</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedEmployment.jenisUsaha}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Gaji Pertama</label>
                  <p className="mt-1 text-sm text-gray-900">
                    Rp {selectedEmployment.gajiPertama?.toLocaleString('id-ID') || '-'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Gaji Saat Ini</label>
                  <p className="mt-1 text-sm text-gray-900">
                    Rp {selectedEmployment.gajiSaatIni?.toLocaleString('id-ID') || '-'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Tanggal Mulai Kerja</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {selectedEmployment.tanggalMulaiKerja ?
                      new Date(selectedEmployment.tanggalMulaiKerja).toLocaleDateString('id-ID') : '-'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Status Pekerjaan</label>
                  <p className="mt-1">
                    <Badge variant={
                      selectedEmployment.statusPekerjaan === 'bekerja' ? 'default' :
                      selectedEmployment.statusPekerjaan === 'wirausaha' ? 'secondary' : 'destructive'
                    }>
                      {selectedEmployment.statusPekerjaan === 'bekerja' ? 'Bekerja' :
                       selectedEmployment.statusPekerjaan === 'wirausaha' ? 'Wirausaha' : 'Tidak Bekerja'}
                    </Badge>
                  </p>
                </div>
                <div className="md:col-span-2">
                  <label className="text-sm font-medium text-gray-700">Alamat Perusahaan</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedEmployment.alamatPerusahaan || '-'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Tanggal Dibuat</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {new Date(selectedEmployment.createdAt).toLocaleDateString('id-ID')}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Terakhir Diupdate</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {new Date(selectedEmployment.updatedAt).toLocaleDateString('id-ID')}
                  </p>
                </div>
              </div>

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
                  Tutup
                </Button>
                <Button onClick={() => {
                  setIsViewDialogOpen(false);
                  handleEditEmployment(selectedEmployment);
                }}>
                  Edit Data
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </Layout>
  );
};

export default Employment;
