import React, { useState, useEffect } from 'react';
import { CheckCircle, XCircle, Clock, Eye, FileText } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import Layout from '@/components/Layout';
import { dataManager, AlumniData } from '@/utils/dataManager';

const AlumniVerification = () => {
  const [alumni, setAlumni] = useState<AlumniData[]>([]);
  const [selectedAlumni, setSelectedAlumni] = useState<AlumniData | null>(null);
  const [verificationNote, setVerificationNote] = useState('');

  useEffect(() => {
    loadPendingAlumni();
  }, []);

  const loadPendingAlumni = () => {
    const data = dataManager.getAlumniData().filter(a => a.statusVerifikasi === 'pending');
    setAlumni(data);
  };

  const handleVerification = (alumniId: string, status: 'verified' | 'rejected') => {
    dataManager.updateAlumni(alumniId, { 
      statusVerifikasi: status,
      updatedAt: new Date().toISOString()
    });
    loadPendingAlumni();
    setSelectedAlumni(null);
    setVerificationNote('');
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'verified':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'rejected':
        return <XCircle className="h-5 w-5 text-red-600" />;
      default:
        return <Clock className="h-5 w-5 text-yellow-600" />;
    }
  };

  return (
    <Layout>
      <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Verifikasi Data Alumni</h1>
        <p className="text-gray-600">Verifikasi dan validasi data alumni yang baru mendaftar</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Menunggu Verifikasi</p>
                <p className="text-2xl font-bold text-gray-900">{alumni.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Terverifikasi</p>
                <p className="text-2xl font-bold text-gray-900">
                  {dataManager.getAlumniData().filter(a => a.statusVerifikasi === 'verified').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <XCircle className="h-8 w-8 text-red-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Ditolak</p>
                <p className="text-2xl font-bold text-gray-900">
                  {dataManager.getAlumniData().filter(a => a.statusVerifikasi === 'rejected').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Pending Verification List */}
      <Card>
        <CardHeader>
          <CardTitle>Alumni Menunggu Verifikasi</CardTitle>
        </CardHeader>
        <CardContent>
          {alumni.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Clock className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>Tidak ada alumni yang menunggu verifikasi</p>
            </div>
          ) : (
            <div className="space-y-4">
              {alumni.map((item) => (
                <div key={item.id} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-4">
                        <div className="flex-1">
                          <h3 className="font-medium text-gray-900">{item.namaLengkap}</h3>
                          <p className="text-sm text-gray-600">NIM: {item.nim}</p>
                          <p className="text-sm text-gray-600">{item.programStudi} - {item.fakultas}</p>
                          <p className="text-sm text-gray-600">Tahun Lulus: {item.tahunLulus} | IPK: {item.ipk}</p>
                        </div>
                        <div className="text-right">
                          <Badge className="bg-yellow-100 text-yellow-800">
                            {getStatusIcon(item.statusVerifikasi)}
                            <span className="ml-1">Menunggu</span>
                          </Badge>
                          <p className="text-xs text-gray-500 mt-1">
                            Daftar: {new Date(item.createdAt).toLocaleDateString('id-ID')}
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="flex space-x-2 ml-4">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="outline" size="sm" onClick={() => setSelectedAlumni(item)}>
                            <Eye className="h-4 w-4 mr-1" />
                            Detail
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                          <DialogHeader>
                            <DialogTitle>Detail Alumni - {item.namaLengkap}</DialogTitle>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <label className="text-sm font-medium text-gray-700">NIM</label>
                                <p className="text-sm text-gray-900">{item.nim}</p>
                              </div>
                              <div>
                                <label className="text-sm font-medium text-gray-700">Email</label>
                                <p className="text-sm text-gray-900">{item.email}</p>
                              </div>
                              <div>
                                <label className="text-sm font-medium text-gray-700">Program Studi</label>
                                <p className="text-sm text-gray-900">{item.programStudi}</p>
                              </div>
                              <div>
                                <label className="text-sm font-medium text-gray-700">Fakultas</label>
                                <p className="text-sm text-gray-900">{item.fakultas}</p>
                              </div>
                              <div>
                                <label className="text-sm font-medium text-gray-700">Tahun Masuk</label>
                                <p className="text-sm text-gray-900">{item.tahunMasuk}</p>
                              </div>
                              <div>
                                <label className="text-sm font-medium text-gray-700">Tahun Lulus</label>
                                <p className="text-sm text-gray-900">{item.tahunLulus}</p>
                              </div>
                              <div>
                                <label className="text-sm font-medium text-gray-700">IPK</label>
                                <p className="text-sm text-gray-900">{item.ipk}</p>
                              </div>
                              <div>
                                <label className="text-sm font-medium text-gray-700">No. Telepon</label>
                                <p className="text-sm text-gray-900">{item.noTelepon}</p>
                              </div>
                            </div>
                            <div>
                              <label className="text-sm font-medium text-gray-700">Alamat</label>
                              <p className="text-sm text-gray-900">{item.alamat}</p>
                            </div>
                            
                            <div className="border-t pt-4">
                              <label className="text-sm font-medium text-gray-700">Catatan Verifikasi</label>
                              <Textarea
                                placeholder="Tambahkan catatan verifikasi (opsional)"
                                value={verificationNote}
                                onChange={(e) => setVerificationNote(e.target.value)}
                                className="mt-1"
                              />
                            </div>
                            
                            <div className="flex justify-end space-x-2 pt-4">
                              <Button
                                variant="outline"
                                onClick={() => handleVerification(item.id, 'rejected')}
                                className="text-red-600 border-red-600 hover:bg-red-50"
                              >
                                <XCircle className="h-4 w-4 mr-1" />
                                Tolak
                              </Button>
                              <Button
                                onClick={() => handleVerification(item.id, 'verified')}
                                className="bg-green-600 hover:bg-green-700"
                              >
                                <CheckCircle className="h-4 w-4 mr-1" />
                                Verifikasi
                              </Button>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
      </div>
    </Layout>
  );
};

export default AlumniVerification;
